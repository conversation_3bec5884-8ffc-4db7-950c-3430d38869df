/**
 * 物理抓取组件
 * 用于处理物理对象的抓取
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3, Quaternion } from 'three';
import { BodyType } from '../../physics/types/BodyType';
import { TransformComponent  } from '../../core/TransformComponent';

// 临时定义缺失的类型
export enum Hand {
  LEFT = 'left',
  RIGHT = 'right'
}

// 临时定义物理体组件接口
interface PhysicsBodyComponent extends Component {
  bodyType: BodyType;
  position: Vector3;
  linearVelocity: Vector3;
  setKinematicTarget(position: Vector3, rotation: Quaternion): void;
  applyForce(x: number, y: number, z: number): void;
}

/**
 * 物理抓取组件配置
 */
export interface PhysicsGrabComponentConfig {
  /** 抓取力 */
  grabForce?: number;
  /** 抓取阻尼 */
  grabDamping?: number;
  /** 是否保持原始物理类型 */
  keepOriginalBodyType?: boolean;
  /** 抓取时的物理类型 */
  grabBodyType?: BodyType;
}

/**
 * 物理抓取组件
 */
export class PhysicsGrabComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'PhysicsGrabComponent';

  /** 抓取力 */
  private _grabForce: number;

  /** 抓取阻尼 */
  private _grabDamping: number;

  /** 是否保持原始物理类型 */
  private _keepOriginalBodyType: boolean;

  /** 抓取时的物理类型 */
  private _grabBodyType: BodyType;

  /** 原始物理类型 */
  private _originalBodyType?: BodyType;

  /** 抓取者 */
  private _grabber?: Entity;

  /** 抓取手 */
  private _hand?: Hand;

  /** 抓取偏移 */
  private _grabOffset: Vector3 = new Vector3();

  /** 抓取旋转偏移 */
  private _grabRotationOffset: Quaternion = new Quaternion();

  /** 是否被抓取 */
  private _isGrabbed: boolean = false;

  /**
   * 构造函数
   * @param config 组件配置
   */
  constructor(config: PhysicsGrabComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super(PhysicsGrabComponent.TYPE);

    // 初始化属性
    this._grabForce = config.grabForce || 10.0;
    this._grabDamping = config.grabDamping || 0.5;
    this._keepOriginalBodyType = config.keepOriginalBodyType !== undefined ? config.keepOriginalBodyType : false;
    this._grabBodyType = config.grabBodyType || BodyType.KINEMATIC;
  }

  /**
   * 获取抓取力
   */
  get grabForce(): number {
    return this._grabForce;
  }

  /**
   * 设置抓取力
   */
  set grabForce(value: number) {
    this._grabForce = value;
  }

  /**
   * 获取抓取阻尼
   */
  get grabDamping(): number {
    return this._grabDamping;
  }

  /**
   * 设置抓取阻尼
   */
  set grabDamping(value: number) {
    this._grabDamping = value;
  }

  /**
   * 获取是否保持原始物理类型
   */
  get keepOriginalBodyType(): boolean {
    return this._keepOriginalBodyType;
  }

  /**
   * 设置是否保持原始物理类型
   */
  set keepOriginalBodyType(value: boolean) {
    this._keepOriginalBodyType = value;
  }

  /**
   * 获取抓取时的物理类型
   */
  get grabBodyType(): BodyType {
    return this._grabBodyType;
  }

  /**
   * 设置抓取时的物理类型
   */
  set grabBodyType(value: BodyType) {
    this._grabBodyType = value;
  }

  /**
   * 获取是否被抓取
   */
  get isGrabbed(): boolean {
    return this._isGrabbed;
  }

  /**
   * 获取抓取者
   */
  get grabber(): Entity | undefined {
    return this._grabber;
  }

  /**
   * 获取抓取手
   */
  get hand(): Hand | undefined {
    return this._hand;
  }

  /**
   * 获取抓取偏移
   */
  get grabOffset(): Vector3 {
    return this._grabOffset.clone();
  }

  /**
   * 获取抓取旋转偏移
   */
  get grabRotationOffset(): Quaternion {
    return this._grabRotationOffset.clone();
  }

  /**
   * 开始抓取
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 是否成功
   */
  startGrab(grabber: Entity, hand: Hand): boolean {
    // 如果已经被抓取，则返回失败
    if (this._isGrabbed) {
      return false;
    }

    // 获取物理体组件
    const entity = this.getEntity();
    if (!entity) return false;

    const physicsBody = entity.getComponent('PhysicsBody') as any as any as PhysicsBodyComponent;
    if (!physicsBody) {
      console.warn('PhysicsGrabComponent', `Entity ${entity.id} does not have a PhysicsBodyComponent`);
      return false;
    }

    // 保存原始物理类型
    this._originalBodyType = physicsBody.bodyType;

    // 如果不保持原始物理类型，则更改物理类型
    if (!this._keepOriginalBodyType) {
      physicsBody.bodyType = this._grabBodyType;
    }

    // 设置抓取状态
    this._isGrabbed = true;
    this._grabber = grabber;
    this._hand = hand;

    // 计算抓取偏移
    this.calculateGrabOffset(grabber, hand);

    return true;
  }

  /**
   * 结束抓取
   * @returns 是否成功
   */
  endGrab(): boolean {
    // 如果未被抓取，则返回失败
    if (!this._isGrabbed || !this._grabber) {
      return false;
    }

    // 获取物理体组件
    const entity = this.getEntity();
    if (entity) {
      const physicsBody = entity.getComponent('PhysicsBody') as any as any as PhysicsBodyComponent;
      if (physicsBody && this._originalBodyType !== undefined && !this._keepOriginalBodyType) {
        // 恢复原始物理类型
        physicsBody.bodyType = this._originalBodyType;
      }
    }

    // 重置抓取状态
    this._isGrabbed = false;
    this._grabber = undefined;
    this._hand = undefined;
    this._originalBodyType = undefined;

    return true;
  }

  /**
   * 计算抓取偏移
   * @param grabber 抓取者
   * @param hand 抓取手
   */
  private calculateGrabOffset(grabber: Entity, hand: Hand): void {
    // 获取抓取者的变换组件
    const grabberTransform = grabber.getComponent('Transform') as any;
    if (!grabberTransform) return;

    // 获取被抓取实体的变换组件
    const entityTransform = this.entity.getComponent('Transform') as any as any;
    if (!entityTransform) return;

    // 计算位置偏移
    this._grabOffset.set(
      (entityTransform as any).getPosition().x - (grabberTransform as any).getPosition().x,
      (entityTransform as any).getPosition().y - (grabberTransform as any).getPosition().y,
      (entityTransform as any).getPosition().z - (grabberTransform as any).getPosition().z
    );

    // 计算旋转偏移
    const grabberRotation = new Quaternion(
      grabberTransform.rotation.x,
      grabberTransform.rotation.y,
      grabberTransform.rotation.z,
      grabberTransform.rotation.w
    );
    const entityRotation = new Quaternion(
      entityTransform.rotation.x,
      entityTransform.rotation.y,
      entityTransform.rotation.z,
      entityTransform.rotation.w
    );

    // 计算相对旋转
    this._grabRotationOffset.copy(grabberRotation).invert().multiply(entityRotation);
  }

  /**
   * 更新抓取
   * @param deltaTime 时间增量（秒）
   */
  update(deltaTime: number): void {
    // 如果未被抓取，则返回
    if (!this._isGrabbed || !this._grabber) {
      return;
    }

    // 获取物理体组件
    const physicsBody = this.entity.getComponent('PhysicsBodyComponent') as PhysicsBodyComponent;
    if (!physicsBody) return;

    // 获取抓取者的变换组件
    const grabberTransform = this._grabber.getComponent('Transform') as any;
    if (!grabberTransform) return;

    // 计算目标位置
    const targetPosition = new Vector3(
      (grabberTransform as any).getPosition().x,
      (grabberTransform as any).getPosition().y,
      (grabberTransform as any).getPosition().z
    ).add(this._grabOffset);

    // 计算目标旋转
    const grabberRotation = new Quaternion(
      grabberTransform.rotation.x,
      grabberTransform.rotation.y,
      grabberTransform.rotation.z,
      grabberTransform.rotation.w
    );
    const targetRotation = new Quaternion().copy(grabberRotation).multiply(this._grabRotationOffset);

    // 根据物理类型更新位置和旋转
    if (physicsBody.bodyType === BodyType.KINEMATIC) {
      // 直接设置位置和旋转
      physicsBody.setKinematicTarget(targetPosition, targetRotation);
    } else {
      // 应用力和扭矩
      const currentPosition = new Vector3(
        (physicsBody as any).getPosition().x,
        (physicsBody as any).getPosition().y,
        (physicsBody as any).getPosition().z
      );
      const direction = new Vector3().subVectors(targetPosition, currentPosition);
      const distance = direction.length();
      direction.normalize();

      // 计算力
      const force = direction.multiplyScalar(distance * this._grabForce);
      physicsBody.applyForce(force.x, force.y, force.z);

      // 应用阻尼
      const velocity = new Vector3(
        physicsBody.linearVelocity.x,
        physicsBody.linearVelocity.y,
        physicsBody.linearVelocity.z
      );
      const dampingForce = velocity.multiplyScalar(-this._grabDamping);
      physicsBody.applyForce(dampingForce.x, dampingForce.y, dampingForce.z);
    }
  }
}
