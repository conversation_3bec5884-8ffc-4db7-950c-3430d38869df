/**
 * 可抓取组件
 * 用于标记可被抓取的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3, Quaternion } from 'three';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 抓取类型
 */
export enum GrabType {
  /** 直接抓取 - 物体直接附加到抓取者手上 */
  DIRECT = 'direct',
  /** 距离抓取 - 物体保持一定距离 */
  DISTANCE = 'distance',
  /** 弹簧抓取 - 物体通过弹簧约束连接 */
  SPRING = 'spring'
}

/**
 * 抓取手
 */
export enum Hand {
  /** 左手 */
  LEFT = 'left',
  /** 右手 */
  RIGHT = 'right',
  /** 任意手 */
  ANY = 'any'
}

/**
 * 可抓取组件配置
 */
export interface GrabbableComponentConfig {
  /** 抓取类型 */
  grabType?: GrabType;
  /** 允许的抓取手 */
  allowedHands?: Hand[];
  /** 是否可抓取 */
  grabbable?: boolean;
  /** 抓取距离 */
  grabDistance?: number;
  /** 抓取声音 */
  grabSound?: string;
  /** 释放声音 */
  releaseSound?: string;
  /** 抓取回调 */
  onGrab?: (entity: Entity, grabber: Entity) => void;
  /** 释放回调 */
  onRelease?: (entity: Entity, grabber: Entity) => void;
}

/**
 * 可抓取组件
 */
export class GrabbableComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabbableComponent';

  /** 抓取类型 */
  private _grabType: GrabType;

  /** 允许的抓取手 */
  private _allowedHands: Hand[];

  /** 是否可抓取 */
  private _grabbable: boolean;

  /** 抓取距离 */
  private _grabDistance: number;

  /** 抓取声音 */
  private _grabSound?: string;

  /** 释放声音 */
  private _releaseSound?: string;

  /** 是否被抓取 */
  private _isGrabbed: boolean = false;

  /** 抓取者 */
  private _grabber?: Entity;

  /** 抓取手 */
  private _grabbedHand?: Hand;

  /** 原始父实体 */
  private _originalParent?: Entity;

  /** 原始位置 */
  private _originalPosition: Vector3 = new Vector3();

  /** 原始旋转 */
  private _originalRotation: Quaternion = new Quaternion();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: GrabbableComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super(GrabbableComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    // 初始化属性
    this._grabType = config.grabType || GrabType.DIRECT;
    this._allowedHands = config.allowedHands || [Hand.ANY];
    this._grabbable = config.grabbable !== undefined ? config.grabbable : true;
    this._grabDistance = config.grabDistance || 0.1;
    this._grabSound = config.grabSound;
    this._releaseSound = config.releaseSound;

    // 注册回调
    if (config.onGrab) {
      this.on('grab', config.onGrab);
    }

    if (config.onRelease) {
      this.on('release', config.onRelease);
    }
  }

  /**
   * 获取抓取类型
   */
  get grabType(): GrabType {
    return this._grabType;
  }

  /**
   * 设置抓取类型
   */
  set grabType(value: GrabType) {
    this._grabType = value;
  }

  /**
   * 获取允许的抓取手
   */
  get allowedHands(): Hand[] {
    return this._allowedHands;
  }

  /**
   * 设置允许的抓取手
   */
  set allowedHands(value: Hand[]) {
    this._allowedHands = value;
  }

  /**
   * 获取是否可抓取
   */
  get grabbable(): boolean {
    return this._grabbable;
  }

  /**
   * 设置是否可抓取
   */
  set grabbable(value: boolean) {
    this._grabbable = value;
  }

  /**
   * 获取抓取距离
   */
  get grabDistance(): number {
    return this._grabDistance;
  }

  /**
   * 设置抓取距离
   */
  set grabDistance(value: number) {
    this._grabDistance = value;
  }

  /**
   * 获取是否被抓取
   */
  get isGrabbed(): boolean {
    return this._isGrabbed;
  }

  /**
   * 获取抓取者
   */
  get grabber(): Entity | undefined {
    return this._grabber;
  }

  /**
   * 获取抓取手
   */
  get grabbedHand(): Hand | undefined {
    return this._grabbedHand;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event: string, callback: EventCallback): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event: string, callback?: EventCallback): this {
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 被抓取
   * @param grabber 抓取者
   * @param hand 抓取手
   * @returns 是否抓取成功
   */
  grab(grabber: Entity, hand: Hand = Hand.RIGHT): boolean {
    // 如果不可抓取或已被抓取，则返回失败
    if (!this._grabbable || this._isGrabbed) {
      return false;
    }

    // 检查是否允许使用指定的手抓取
    if (!this._allowedHands.includes(Hand.ANY) && !this._allowedHands.includes(hand)) {
      return false;
    }

    // 保存原始父实体
    this._originalParent = this.entity.getParent();

    // 保存原始位置和旋转
    const transform = this.entity.getComponent('Transform') as any as any;
    if (transform) {
      this._originalPosition.copy(transform.position);
      this._originalRotation.copy(transform.rotation);
    }

    // 设置抓取状态
    this._isGrabbed = true;
    this._grabber = grabber;
    this._grabbedHand = hand;

    // 触发抓取事件
    this.eventEmitter.emit('grab', this.entity, grabber);

    return true;
  }

  /**
   * 释放
   * @returns 是否释放成功
   */
  release(): boolean {
    // 如果未被抓取，则返回失败
    if (!this._isGrabbed || !this._grabber) {
      return false;
    }

    const grabber = this._grabber;

    // 重置抓取状态
    this._isGrabbed = false;
    this._grabber = undefined;
    this._grabbedHand = undefined;

    // 触发释放事件
    this.eventEmitter.emit('release', this.entity, grabber);

    return true;
  }
}
