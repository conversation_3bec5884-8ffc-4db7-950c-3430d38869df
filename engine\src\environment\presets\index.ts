/**
 * 环境预设模块
 *
 * 提供各种环境预设，包括天气、地形、光照等
 */

import { EnvironmentType, WeatherType, TerrainType } from '../components/EnvironmentAwarenessComponent';
import { ResponseType, ResponsePriority, EnvironmentResponseRule } from '../components/EnvironmentResponseComponent';

/**
 * 环境预设接口
 */
export interface EnvironmentPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description?: string;
  /** 环境类型 */
  environmentType: EnvironmentType;
  /** 天气类型 */
  weatherType: WeatherType;
  /** 地形类型 */
  terrainType?: TerrainType;
  /** 光照参数 */
  lighting?: {
    /** 环境光颜色 */
    ambientColor?: number;
    /** 环境光强度 */
    ambientIntensity?: number;
    /** 方向光颜色 */
    directionalColor?: number;
    /** 方向光强度 */
    directionalIntensity?: number;
    /** 雾颜色 */
    fogColor?: number;
    /** 雾密度 */
    fogDensity?: number;
  };
  /** 粒子系统参数 */
  particles?: {
    /** 粒子类型 */
    type: string;
    /** 粒子数量 */
    count: number;
    /** 粒子大小 */
    size: number;
    /** 粒子颜色 */
    color: number;
    /** 粒子生命周期 */
    lifetime: number;
    /** 粒子速度 */
    speed: number;
  }[];
  /** 声音参数 */
  sounds?: {
    /** 声音ID */
    id: string;
    /** 声音URL */
    url: string;
    /** 声音音量 */
    volume: number;
    /** 是否循环 */
    loop: boolean;
  }[];
  /** 响应规则 */
  responseRules?: EnvironmentResponseRule[];
}

/**
 * 创建雨天天气预设
 * @returns 雨天天气预设
 */
export function createRainyWeatherPreset(): EnvironmentPreset {
  return {
    id: 'preset_rainy_weather',
    name: '雨天天气',
    description: '雨天天气预设，包括雨滴粒子、雨声音效和角色响应',
    environmentType: EnvironmentType.OUTDOOR,
    weatherType: WeatherType.RAINY,
    lighting: {
      ambientColor: 0x555566,
      ambientIntensity: 0.5,
      directionalColor: 0x8888aa,
      directionalIntensity: 0.7,
      fogColor: 0x8899aa,
      fogDensity: 0.05
    },
    particles: [
      {
        type: 'rain',
        count: 5000,
        size: 0.1,
        color: 0xaaaaff,
        lifetime: 2,
        speed: 10
      },
      {
        type: 'splash',
        count: 500,
        size: 0.05,
        color: 0xffffff,
        lifetime: 0.5,
        speed: 1
      }
    ],
    sounds: [
      {
        id: 'rain_ambient',
        url: 'sounds/rain_ambient.mp3',
        volume: 0.5,
        loop: true
      },
      {
        id: 'thunder',
        url: 'sounds/thunder.mp3',
        volume: 0.7,
        loop: false
      }
    ],
    responseRules: [
      {
        id: 'rainy_animation_response',
        name: '雨天动画响应',
        description: '角色在雨中的动画响应',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [
          {
            type: 'weather',
            params: { weatherType: WeatherType.RAINY },
            evaluate: (data) => data.weatherType === WeatherType.RAINY
          },
          {
            type: 'environment',
            params: { environmentType: EnvironmentType.OUTDOOR },
            evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
          }
        ],
        actions: [
          {
            type: 'play_animation',
            params: { animationName: 'rain_reaction', blendTime: 0.5, loop: true },
            execute: (entity) => {
              // 注意：这里需要实际的动画系统类，暂时注释掉
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.playAnimation(entity, 'rain_reaction', { blendTime: 0.5, loop: true });
              // }
              console.log('播放雨天反应动画:', entity.id);
            },
            stop: (entity) => {
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.stopAnimation(entity, 'rain_reaction', { blendTime: 0.5 });
              // }
              console.log('停止雨天反应动画:', entity.id);
            }
          },
          {
            type: 'play_sound',
            params: { soundId: 'character_rain_reaction', volume: 0.5, loop: true },
            execute: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.playSound('character_rain_reaction', { volume: 0.5, loop: true, attachTo: entity });
              // }
              console.log('播放雨天反应音效:', entity.id);
            },
            stop: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.stopSound('character_rain_reaction');
              // }
              console.log('停止雨天反应音效:', entity.id);
            }
          }
        ],
        cooldown: 5000,
        enabled: true
      }
    ]
  };
}

/**
 * 创建雪天天气预设
 * @returns 雪天天气预设
 */
export function createSnowyWeatherPreset(): EnvironmentPreset {
  return {
    id: 'preset_snowy_weather',
    name: '雪天天气',
    description: '雪天天气预设，包括雪花粒子、风声音效和角色响应',
    environmentType: EnvironmentType.OUTDOOR,
    weatherType: WeatherType.SNOWY,
    lighting: {
      ambientColor: 0xccccdd,
      ambientIntensity: 0.7,
      directionalColor: 0xeeeeff,
      directionalIntensity: 0.6,
      fogColor: 0xddddee,
      fogDensity: 0.03
    },
    particles: [
      {
        type: 'snow',
        count: 3000,
        size: 0.1,
        color: 0xffffff,
        lifetime: 5,
        speed: 2
      }
    ],
    sounds: [
      {
        id: 'wind_ambient',
        url: 'sounds/wind_ambient.mp3',
        volume: 0.4,
        loop: true
      }
    ],
    responseRules: [
      {
        id: 'snowy_animation_response',
        name: '雪天动画响应',
        description: '角色在雪中的动画响应',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [
          {
            type: 'weather',
            params: { weatherType: WeatherType.SNOWY },
            evaluate: (data) => data.weatherType === WeatherType.SNOWY
          },
          {
            type: 'environment',
            params: { environmentType: EnvironmentType.OUTDOOR },
            evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
          }
        ],
        actions: [
          {
            type: 'play_animation',
            params: { animationName: 'snow_reaction', blendTime: 0.5, loop: true },
            execute: (entity) => {
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.playAnimation(entity, 'snow_reaction', { blendTime: 0.5, loop: true });
              // }
              console.log('播放雪天反应动画:', entity.id);
            },
            stop: (entity) => {
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.stopAnimation(entity, 'snow_reaction', { blendTime: 0.5 });
              // }
              console.log('停止雪天反应动画:', entity.id);
            }
          },
          {
            type: 'play_sound',
            params: { soundId: 'character_snow_reaction', volume: 0.5, loop: true },
            execute: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.playSound('character_snow_reaction', { volume: 0.5, loop: true, attachTo: entity });
              // }
              console.log('播放雪天反应音效:', entity.id);
            },
            stop: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.stopSound('character_snow_reaction');
              // }
              console.log('停止雪天反应音效:', entity.id);
            }
          }
        ],
        cooldown: 5000,
        enabled: true
      }
    ]
  };
}

/**
 * 创建炎热天气预设
 * @returns 炎热天气预设
 */
export function createHotWeatherPreset(): EnvironmentPreset {
  return {
    id: 'preset_hot_weather',
    name: '炎热天气',
    description: '炎热天气预设，包括热浪特效和角色响应',
    environmentType: EnvironmentType.OUTDOOR,
    weatherType: WeatherType.SUNNY,
    lighting: {
      ambientColor: 0xffeecc,
      ambientIntensity: 0.8,
      directionalColor: 0xffffee,
      directionalIntensity: 1.2,
      fogColor: 0xffeecc,
      fogDensity: 0.02
    },
    particles: [
      {
        type: 'heat_wave',
        count: 100,
        size: 5,
        color: 0xffffff,
        lifetime: 3,
        speed: 0.5
      }
    ],
    sounds: [
      {
        id: 'cicada_ambient',
        url: 'sounds/cicada_ambient.mp3',
        volume: 0.3,
        loop: true
      }
    ],
    responseRules: [
      {
        id: 'hot_animation_response',
        name: '炎热天气动画响应',
        description: '角色在炎热天气中的动画响应',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [
          {
            type: 'weather',
            params: { weatherType: WeatherType.SUNNY },
            evaluate: (data) => data.weatherType === WeatherType.SUNNY
          },
          {
            type: 'environment',
            params: { environmentType: EnvironmentType.OUTDOOR },
            evaluate: (data) => data.environmentType === EnvironmentType.OUTDOOR
          },
          {
            type: 'temperature',
            params: { minTemperature: 30 },
            evaluate: (data) => (data.temperature || 0) >= 30
          }
        ],
        actions: [
          {
            type: 'play_animation',
            params: { animationName: 'hot_reaction', blendTime: 0.5, loop: true },
            execute: (entity) => {
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.playAnimation(entity, 'hot_reaction', { blendTime: 0.5, loop: true });
              // }
              console.log('播放炎热天气反应动画:', entity.id);
            },
            stop: (entity) => {
              // const animSystem = entity.getWorld().getSystem(AnimationSystem);
              // if (animSystem) {
              //   animSystem.stopAnimation(entity, 'hot_reaction', { blendTime: 0.5 });
              // }
              console.log('停止炎热天气反应动画:', entity.id);
            }
          },
          {
            type: 'play_sound',
            params: { soundId: 'character_hot_reaction', volume: 0.5, loop: true },
            execute: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.playSound('character_hot_reaction', { volume: 0.5, loop: true, attachTo: entity });
              // }
              console.log('播放炎热天气反应音效:', entity.id);
            },
            stop: (entity) => {
              // const audioSystem = entity.getWorld().getSystem(AudioSystem);
              // if (audioSystem) {
              //   audioSystem.stopSound('character_hot_reaction');
              // }
              console.log('停止炎热天气反应音效:', entity.id);
            }
          }
        ],
        cooldown: 10000,
        enabled: true
      }
    ]
  };
}
